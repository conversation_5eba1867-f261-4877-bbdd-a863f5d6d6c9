import { <PERSON>E<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>N<PERSON>Empty, <PERSON><PERSON>ptional, IsString, Length, Matches } from 'class-validator';

import { ToLowerCase } from '@/common/decorators/transforms.decorator';
import { Language } from '@/common/enums/language.enum';
import { ApiProperty } from '@nestjs/swagger';

export class SendPhoneOtpDto {
  @ApiProperty({ example: '987654321', description: 'Phone number without country code' })
  @Matches(/^[0-9]{9,10}$/, { message: 'Phone number must be 9-10 digits' })
  @IsNotEmpty()
  phone: string;

  @ApiProperty({ example: '+84' })
  @IsString()
  @IsNotEmpty()
  phoneCountryCode: string;
}

export class VerifyPhoneOtpDto {
  @ApiProperty({ example: '987654321', description: 'Phone number without country code' })
  @Matches(/^[0-9]{9,10}$/, { message: 'Phone number must be 9-10 digits' })
  @IsNotEmpty()
  phone: string;

  @ApiProperty({ example: '123456' })
  @IsString()
  @Length(6, 6, { message: 'OTP must be 6 digits' })
  otp: string;

  @ApiProperty({ example: '+84' })
  @IsString()
  @IsNotEmpty()
  phoneCountryCode: string;
}

export class SendEmailOtpDto {
  @ApiProperty({ example: '<EMAIL>' })
  @IsEmail({}, { message: 'Invalid email format' })
  @IsNotEmpty()
  @ToLowerCase()
  email: string;

  @ApiProperty({ example: 'verification_token_from_phone_step' })
  @IsString()
  @IsNotEmpty()
  phoneVerificationToken: string;
}

export class VerifyEmailOtpDto {
  @ApiProperty({ example: '<EMAIL>' })
  @IsEmail({}, { message: 'Invalid email format' })
  @IsNotEmpty()
  @ToLowerCase()
  email: string;

  @ApiProperty({ example: '123456' })
  @IsString()
  @Length(6, 6, { message: 'OTP must be 6 digits' })
  otp: string;

  @ApiProperty({ example: 'verification_token_from_phone_step' })
  @IsString()
  @IsNotEmpty()
  phoneVerificationToken: string;
}

export class UpdateProfileDto {
  @ApiProperty({ example: 'John', required: false })
  @IsOptional()
  @IsString()
  @IsNotEmpty()
  firstName?: string;

  @ApiProperty({ example: 'Doe', required: false })
  @IsOptional()
  @IsString()
  @IsNotEmpty()
  lastName?: string;

  @ApiProperty({ enum: Language, example: Language.EN, required: false })
  @IsOptional()
  @IsEnum(Language)
  language?: Language;
}

export class FakeLoginDto {
  @ApiProperty({ example: '<EMAIL>' })
  @IsEmail({}, { message: 'Invalid email format' })
  @IsNotEmpty()
  email: string;
}
