import { Pagination } from 'nestjs-typeorm-paginate';

import { User } from '@/common/decorators/user.decorator';
import { Roles } from '@/modules/auth/decorators/roles.decorator';
import { UserType } from '@/modules/auth/enums/user-type.enum';
import { UserJwtInfo } from '@/modules/auth/types/jwt-payload.type';
import { MenuItem } from '@/modules/menu-items/entities/menu-item.entity';
import { Body, Controller, Delete, Get, Param, ParseUUIDPipe, Post, Put, Query } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';

import { CartsService } from '../carts.service';
import { AddToCartDto } from '../dto/add-to-cart.dto';
import { CartQueryDto } from '../dto/cart-query.dto';
import { UpdateCartItemAmountDto } from '../dto/update-cart-item-amount.dto';
import { Cart } from '../entities/cart.entity';

@ApiTags('(User) Carts')
@Controller('user/carts')
@Roles({ userType: UserType.USER, role: '*' })
export class CartsController {
  constructor(private readonly cartsService: CartsService) {}

  @Get()
  findAll(@User() user: UserJwtInfo, @Query() query: CartQueryDto): Promise<Pagination<Cart>> {
    return this.cartsService.findAll(user, query);
  }

  @Post('add')
  addToCart(@User() user: UserJwtInfo, @Body() addToCartDto: AddToCartDto): Promise<Cart> {
    return this.cartsService.addToCart(user, addToCartDto);
  }

  @Get('cart-by-restaurant/:restaurantId')
  findActiveCartByRestaurant(
    @User() user: UserJwtInfo,
    @Param('restaurantId', ParseUUIDPipe) restaurantId: string,
  ): Promise<Cart | undefined> {
    return this.cartsService.findActiveCartByRestaurant(user, restaurantId);
  }

  @Get('you-might-also-like/:restaurantId')
  getYouMightAlsoLike(
    @User() user: UserJwtInfo,
    @Param('restaurantId', ParseUUIDPipe) restaurantId: string,
  ): Promise<MenuItem[]> {
    return this.cartsService.getYouMightAlsoLike(user, restaurantId);
  }

  @Put('item/:cartItemId')
  updateCartItemAmount(
    @User() user: UserJwtInfo,
    @Param('cartItemId', ParseUUIDPipe) cartItemId: string,
    @Body() updateCartItemAmountDto: UpdateCartItemAmountDto,
  ): Promise<Cart> {
    return this.cartsService.updateCartItemAmount(user, cartItemId, updateCartItemAmountDto);
  }

  @Delete('item/:cartItemId')
  removeCartItem(@User() user: UserJwtInfo, @Param('cartItemId', ParseUUIDPipe) cartItemId: string): Promise<Cart> {
    return this.cartsService.removeCartItem(user, cartItemId);
  }

  @Delete(':id')
  deleteCart(@User() user: UserJwtInfo, @Param('id', ParseUUIDPipe) id: string): Promise<boolean> {
    return this.cartsService.deleteCart(user, id);
  }
}
