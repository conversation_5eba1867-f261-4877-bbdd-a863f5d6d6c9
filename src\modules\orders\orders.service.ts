import type { MenuItem } from '@/modules/menu-items/entities/menu-item.entity';
import { DataSource, EntityManager, Repository } from 'typeorm';
import { QueryDeepPartialEntity } from 'typeorm/query-builder/QueryPartialEntity';

import { paginateQueryBuilder } from '@/helpers/queryBuilder';
import { getCurrentTimeByTimeAndDay } from '@/helpers/time';
import { UserType } from '@/modules/auth/enums/user-type.enum';
import { AllUserJwtInfo, UserJwtInfo } from '@/modules/auth/types/jwt-payload.type';
import { CartsService } from '@/modules/carts/carts.service';
import { CartItemGroupOptionDto } from '@/modules/carts/dto/add-to-cart.dto';
import { CartItem } from '@/modules/carts/entities/cart-item.entity';
import { Cart } from '@/modules/carts/entities/cart.entity';
import { GeofencingService } from '@/modules/geofencing/geofencing.service';
import { MenuItemsService } from '@/modules/menu-items/menu-items.service';
import { PaymentOnepayStatus } from '@/modules/onepay/onepay.constants';
import { OnePayService } from '@/modules/onepay/onepay.service';
import { PaymentCardService } from '@/modules/payment-card/payment-card.service';
import { RestaurantReviewsService } from '@/modules/restaurant-reviews/restaurant-reviews.service';
import { RestaurantsService } from '@/modules/restaurants/restaurants.service';
import { MicroserviceClientService } from '@/modules/shared/microservice/microservice-client.service';
import { UserAddressesService } from '@/modules/user-addresses/user-addresses.service';
import { UsersService } from '@/modules/users/users.service';
import { WrapperType } from '@/types/global-types';
import { BadRequestException, forwardRef, Inject, Injectable, Logger, NotFoundException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';

import { MenuSection } from '../menu-sections/entities/menu-section.entity';
import { Restaurant } from '../restaurants/entities/restaurant.entity';
import { FCMService } from '../shared/fcm/fcm.service';
import { FcmRestaurantType, FcmUserType } from '../shared/fcm/fcm.types';
import {
  OrderCancelReason,
  OrderStatus,
  OrderStatusGroup,
  PaymentMethod,
  PaymentStatus,
} from './constants/order.enums';
import { getStatusesByGroups, validateTransitionsStatus } from './constants/orders.constants';
import { CreateOrderDto } from './dto/create-order.dto';
import { EstimateShippingFeeDto } from './dto/estimate-shipping-fee.dto';
import { ModifyOrderDto } from './dto/modify-order.dto';
import {
  ApproveModificationDto,
  ConfirmOrderReceivedDto,
  RejectOrderDto,
  SetDeliveryEtaDto,
  SetPrepTimeDto,
} from './dto/order-actions.dto';
import { OrderQueryDto } from './dto/order-query.dto';
import { OrderStatusCounts, OrderWithCountsResponse } from './dto/order-response.dto';
import { OrderCustomer } from './entities/order-customer.entity';
import { OrderItemOptionOriginal } from './entities/order-item-option-original.entity';
import { OrderItemOption } from './entities/order-item-option.entity';
import { OrderItemOriginal } from './entities/order-item-original.entity';
import { OrderItem } from './entities/order-item.entity';
import { Order } from './entities/order.entity';
import { localizeOrder, localizeOrderList } from './orders.helpers';

@Injectable()
export class OrdersService {
  private readonly logger = new Logger(OrdersService.name);

  private taxRate: number;
  private taxRateAlcohol: number;

  constructor(
    @InjectRepository(Order)
    private readonly orderRepository: Repository<Order>,
    @InjectRepository(OrderCustomer)
    private readonly orderCustomerRepository: Repository<OrderCustomer>,

    private configService: ConfigService,

    private readonly cartsService: CartsService,
    private readonly dataSource: DataSource,
    private readonly microserviceClient: MicroserviceClientService,
    private readonly fcmService: FCMService,
    @Inject(forwardRef(() => PaymentCardService))
    private readonly paymentCardService: WrapperType<PaymentCardService>,
    @Inject(forwardRef(() => OnePayService))
    private readonly onepayService: WrapperType<OnePayService>,
    private readonly menuItemsService: MenuItemsService,
    private readonly userAddressesService: UserAddressesService,
    @Inject(forwardRef(() => UsersService))
    private usersService: WrapperType<UsersService>,
    private readonly restaurantReviewsService: RestaurantReviewsService,
    private readonly geofencingService: GeofencingService,
    private readonly restaurantsService: RestaurantsService,
  ) {
    this.taxRate = parseInt(this.configService.get('tax.taxRate') ?? '8', 10);
    this.taxRateAlcohol = this.configService.get('tax.taxRateAlcohol') ?? 10;
  }

  private calculateTaxAmount(itemTotalPrice: number, menuItem: MenuItem): number {
    return (itemTotalPrice * (menuItem.isAlcohol ? this.taxRateAlcohol : this.taxRate)) / 100;
  }

  /**
   * Helper method to validate state transition
   */
  private validateStateTransition(currentStatus: OrderStatus, newStatus: OrderStatus, action: string): void {
    if (!validateTransitionsStatus(currentStatus, newStatus)) {
      throw new BadRequestException(`Cannot ${action}: invalid transition from ${currentStatus} to ${newStatus}`);
    }
  }

  // Generate order code in format: {merchant account code}-{restaurant Code}-Year-Month-Day-{Auto Increment}
  // Optimized version using orderSequenceNumber for better performance
  private async generateOrderCode(
    restaurantCode: string,
    brandCode: string,
    restaurantId: string,
  ): Promise<{
    orderCode: string;
    sequenceNumber: number;
  }> {
    if (!brandCode || !restaurantCode) {
      throw new BadRequestException('Missing merchant account code or restaurant code');
    }

    const datePrefix = `${brandCode}-${restaurantCode}`;

    // Get the latest sequence number for this restaurant (much faster than LIKE query)
    const latestOrder = await this.orderRepository
      .createQueryBuilder('order')
      .where('order.restaurantId = :restaurantId', { restaurantId })
      .orderBy('order.orderSequenceNumber', 'DESC')
      .getOne();

    const nextSequenceNumber = latestOrder ? latestOrder.orderSequenceNumber + 1 : 1;

    // Ensure sequence number is within range 00001-99999
    if (nextSequenceNumber > 99999) {
      throw new BadRequestException('Order limit exceeded (99999)');
    }

    const incrementStr = String(nextSequenceNumber).padStart(5, '0');
    const orderCode = `${datePrefix}-${incrementStr}`;

    return {
      orderCode,
      sequenceNumber: nextSequenceNumber,
    };
  }

  private convertCartItemOptionsToGroupOptions(cartItem: CartItem): any[] {
    if (!cartItem.cartItemOptions || cartItem.cartItemOptions.length === 0) {
      return [];
    }

    const groupedOptions = new Map<string, CartItemGroupOptionDto>();

    cartItem.cartItemOptions.forEach((option) => {
      const groupId = option.menuItemOptionGroupId;

      if (!groupedOptions.has(groupId)) {
        groupedOptions.set(groupId, {
          optionGroupId: groupId,
          options: [],
        });
      }

      const group = groupedOptions.get(groupId);
      if (group) {
        group.options.push({
          id: option.menuItemOptionId,
          amount: option.amount,
        });
      }
    });

    return Array.from(groupedOptions.values());
  }

  async createOrder(user: UserJwtInfo, createOrderDto: CreateOrderDto): Promise<Order> {
    const { userAddressId, cartId, paymentMethod, note, paymentCardId, includeUtensils } = createOrderDto;
    const userId = user.id;
    const userData = await this.usersService.findById(userId);

    const cart = await this.cartsService.findCartWithMenuItemOptions(cartId, user);

    if (!cart.restaurant.activeAt) {
      throw new BadRequestException('Restaurant is not active');
    }

    if (cart.completedAt) {
      throw new BadRequestException('Cart has already been used to create an order');
    }

    if (!cart?.cartItems?.length) {
      throw new BadRequestException('Cannot create order: cart is empty');
    }

    // Validate restaurant is open
    this.validateRestaurantIsOpen(cart.restaurant);

    for (const cartItem of cart.cartItems) {
      const menuItem = cartItem.menuItem;

      if (!menuItem) {
        throw new BadRequestException('Menu item not found or has been removed');
      }

      this.cartsService.validateMenuItemBelongsToRestaurant(menuItem, cart.restaurantId);

      this.cartsService.validateMenuItemAvailability(menuItem);

      // Additional validation: check if menu section is open
      this.validateMenuSectionIsOpen(cartItem.menuSection);

      const groupOptions = this.convertCartItemOptionsToGroupOptions(cartItem);
      this.cartsService.validateMenuItemOptions(menuItem, groupOptions);
    }

    let subtotal = 0;
    let taxAmount = 0;
    if (paymentMethod === PaymentMethod.CREDIT_CARD) {
      const paymentCard = await this.paymentCardService.findByIdAndUserId(paymentCardId || '', cart.userId);
      if (!paymentCard) {
        throw new BadRequestException('Payment card not found');
      }
    }

    let oderCustomer: OrderCustomer;
    if (userAddressId) {
      const userAddress = await this.userAddressesService.findOneByUserAndId(userAddressId, userId);
      oderCustomer = this.orderCustomerRepository.create({
        name: userData.firstName + ' ' + userData.lastName,
        phone: userData.phone,
        email: userData.email,
        latitude: userAddress.latitude,
        longitude: userAddress.longitude,
        fullAddress: userAddress.fullAddress,
        streetNumber: userAddress.streetNumber,
        streetName: userAddress.streetName,
        city: userAddress.city,
        state: userAddress.state,
        country: userAddress.country,
        postalCode: userAddress.postalCode,
        addressType: userAddress.addressType,
        addressLabel: userAddress.addressLabel,
        placeId: userAddress.placeId,
      });
    } else {
      const temporaryAddress = await this.userAddressesService.getTemporaryAddress(userId);

      oderCustomer = this.orderCustomerRepository.create({
        name: userData.firstName + ' ' + userData.lastName,
        phone: userData.phone,
        email: userData.email,
        latitude: temporaryAddress.latitude,
        longitude: temporaryAddress.longitude,
        fullAddress: temporaryAddress.fullAddress,
        streetNumber: temporaryAddress.streetNumber,
        streetName: temporaryAddress.streetName,
        city: temporaryAddress.city,
        state: temporaryAddress.state,
        country: temporaryAddress.country,
        postalCode: temporaryAddress.postalCode,
        addressType: temporaryAddress.addressType,
        addressLabel: temporaryAddress.addressLabel,
        placeId: temporaryAddress.placeId,
      });
    }

    // Calculate shippingFee based on geofencing
    const geofencings = await this.geofencingService.findGeofencingForPoint(
      cart.restaurantId,
      oderCustomer.latitude,
      oderCustomer.longitude,
    );
    if (!geofencings || geofencings.length === 0) {
      throw new BadRequestException('Your address is outside the delivery area, please select a different address');
    }
    const shippingFee = Math.min(...geofencings.map((g) => Number(g.shippingFee)));

    return await this.dataSource.transaction(async (manager) => {
      const savedCustomer = await manager.save(oderCustomer);

      const { orderCode, sequenceNumber } = await this.generateOrderCode(
        cart.restaurant.code,
        cart.restaurant.brand.code,
        cart.restaurantId,
      );

      const order = manager.create(Order, {
        userId,
        restaurantId: cart.restaurantId,
        cartId: cartId,
        orderCode,
        orderSequenceNumber: sequenceNumber,
        orderCustomerId: savedCustomer.id,
        paymentMethod: paymentMethod,
        note: note,
        paymentCardId: paymentCardId,
        status: OrderStatus.NEW,
        shippingFee,
        restaurantName: cart.restaurant?.publishedName || '',
        ...(includeUtensils ? { includeUtensils: true } : {}),
      } as Order);

      const savedOrder = await manager.save(order);

      for (const cartItem of cart.cartItems) {
        const menuItem = cartItem.menuItem;
        const menuSection = cartItem.menuSection;
        if (!menuItem || !menuSection) throw new BadRequestException('Invalid menu item or menu section');
        const itemPrice = menuItem?.price ?? menuItem.basePrice;
        let itemTotalPrice = itemPrice * cartItem.amount;
        const itemTaxAmount = this.calculateTaxAmount(itemTotalPrice, menuItem);
        taxAmount += itemTaxAmount;

        const orderItemData: Partial<OrderItem> = {
          orderId: savedOrder.id,
          menuItemId: menuItem.id,
          menuSectionId: menuSection.id,
          menuSectionName: menuSection.publishedName,
          menuSectionNameEn: menuSection.publishedNameEn,
          menuSectionNameVi: menuSection.publishedNameVi,
          amount: cartItem.amount,
          price: itemPrice,
          note: cartItem.note,
          menuItemName: menuItem.publishedName,
          menuItemNameEn: menuItem.publishedNameEn,
          menuItemNameVi: menuItem.publishedNameVi,
          menuItemDescription: menuItem.description,
          menuItemDescriptionEn: menuItem.descriptionEn,
          menuItemDescriptionVi: menuItem.descriptionVi,
          menuItemImageUrls: menuItem.imageUrls,
          taxAmount: itemTaxAmount,
          isAlcohol: menuItem.isAlcohol,
        };

        const orderItem = manager.create(OrderItem, orderItemData);
        const orderItemOriginal = manager.create(OrderItemOriginal, orderItemData);

        const [savedOrderItem, savedOrderItemOriginal] = await Promise.all([
          manager.save(orderItem),
          manager.save(orderItemOriginal),
        ]);

        if (cartItem.cartItemOptions && cartItem.cartItemOptions.length > 0) {
          const orderItemOptions: OrderItemOption[] = [];
          const orderItemOptionsOriginal: OrderItemOptionOriginal[] = [];

          for (const cartItemOption of cartItem.cartItemOptions) {
            const menuItemOption = cartItemOption.menuItemOption;
            const menuItemOptionGroup = cartItemOption.menuItemOptionGroup;

            if (!menuItemOption || !menuItemOptionGroup) {
              throw new BadRequestException('Invalid menu item option');
            }

            const optionPrice = menuItemOption.price ?? menuItemOption.basePrice;
            const optionTotalPrice = optionPrice * cartItemOption.amount;
            const optionTaxAmount = this.calculateTaxAmount(optionTotalPrice, menuItemOption);
            taxAmount += optionTaxAmount;

            itemTotalPrice += optionTotalPrice;

            const orderItemOptionData = {
              menuItemOptionGroupId: menuItemOptionGroup.id,
              menuItemOptionId: menuItemOption.id,
              amount: cartItemOption.amount,
              price: optionPrice,
              optionGroupName: menuItemOptionGroup.publishedName,
              optionGroupNameEn: menuItemOptionGroup.publishedNameEn,
              optionGroupNameVi: menuItemOptionGroup.publishedNameVi,
              optionName: menuItemOption.publishedName,
              optionNameEn: menuItemOption.publishedNameEn,
              optionNameVi: menuItemOption.publishedNameVi,
              taxAmount: optionTaxAmount,
              isAlcohol: menuItemOption.isAlcohol,
            };

            const orderItemOption = manager.create(OrderItemOption, {
              orderItemId: savedOrderItem.id,
              ...orderItemOptionData,
            } as OrderItemOption);

            const orderItemOptionOriginal = manager.create(OrderItemOptionOriginal, {
              orderItemOriginalId: savedOrderItemOriginal.id,
              ...orderItemOptionData,
            } as OrderItemOptionOriginal);

            orderItemOptions.push(orderItemOption);
            orderItemOptionsOriginal.push(orderItemOptionOriginal);
          }

          if (orderItemOptions.length > 0 && orderItemOptionsOriginal.length > 0) {
            await Promise.all([manager.save(orderItemOptions), manager.save(orderItemOptionsOriginal)]);
          }
        }

        subtotal += itemTotalPrice;
      }

      const total = Number((subtotal + taxAmount + shippingFee).toFixed(0));

      savedOrder.subtotal = subtotal;
      savedOrder.taxAmount = taxAmount;
      savedOrder.total = total;

      await Promise.all([manager.save(savedOrder), manager.update(Cart, cart.id, { completedAt: new Date() })]);

      const orderWithRelations = await manager.findOne(Order, {
        where: { id: savedOrder.id },
        relations: ['restaurant', 'orderItems', 'orderItems.orderItemOptions', 'orderCustomer'],
      });

      if (!orderWithRelations) {
        throw new NotFoundException('Order not found');
      }

      localizeOrder(orderWithRelations, user.language);

      void Promise.all([
        this.microserviceClient.publishNewOrder(orderWithRelations),
        // Send notification to staff about new order
        this.fcmService.sendToStaffWithRestaurantType(orderWithRelations, FcmRestaurantType.NEW_ORDER),
      ]);

      return orderWithRelations;
    });
  }

  async findAll(
    query: OrderQueryDto,
    user?: UserJwtInfo,
    restaurantId?: string,
    includeStatusCounts?: boolean,
  ): Promise<OrderWithCountsResponse> {
    const {
      page,
      limit,
      search,
      status,
      paymentStatus,
      restaurantId: restaurantIdFilter,
      statusGroups,
      archived,
      isHideUserSeen,
    } = query;

    const queryBuilder = this.orderRepository
      .createQueryBuilder('order')
      .leftJoinAndSelect('order.orderCustomer', 'orderCustomer')
      .leftJoinAndSelect('order.orderItems', 'orderItems')
      .leftJoinAndSelect('orderItems.orderItemOptions', 'orderItemOptions')
      .leftJoinAndSelect('order.orderItemsOriginal', 'orderItemsOriginal')
      .leftJoinAndSelect('orderItemsOriginal.orderItemOptionsOriginal', 'orderItemOptionsOriginal')
      .leftJoinAndSelect('order.chatConversation', 'chatConversation');

    if (search) {
      queryBuilder.leftJoin('order.orderCustomer', 'orderCustomer');
    }

    if (user) {
      queryBuilder.andWhere('order.userId = :userId', { userId: user.id });
    }

    if (restaurantId || restaurantIdFilter) {
      queryBuilder.andWhere('order.restaurantId = :restaurantId', {
        restaurantId: restaurantId || restaurantIdFilter,
      });
    }

    const listStatusFilter: OrderStatus[] = [];

    if (status?.length) {
      listStatusFilter.push(...status);
    }

    if (statusGroups) {
      const statusesInGroups = getStatusesByGroups([statusGroups]);
      if (statusesInGroups.length) listStatusFilter.push(...statusesInGroups);
    }

    if (listStatusFilter.length) {
      queryBuilder.andWhere('order.status IN (:...statuses)', { statuses: Array.from(new Set(listStatusFilter)) });
    }

    if (paymentStatus) {
      queryBuilder.andWhere('order.paymentStatus = :paymentStatus', { paymentStatus });
    }

    if (search) {
      queryBuilder.andWhere(
        '(order.orderCode LIKE :search OR orderCustomer.name LIKE :search OR orderCustomer.phone LIKE :search)',
        { search: `%${search}%` },
      );
    }

    if (!archived) {
      queryBuilder.andWhere('order.archivedAt IS NULL');
    }

    // Hide orders that user has already seen
    if (isHideUserSeen) {
      queryBuilder.andWhere('order.isUserSeen = :isUserSeen', { isUserSeen: false });
    }

    if (statusGroups === OrderStatusGroup.NEEDS_ACTION) {
      queryBuilder
        .addSelect(
          `
          CASE
            WHEN order.status = '${OrderStatus.NOT_DELIVERED}' THEN 1
            WHEN order.status = '${OrderStatus.IN_KITCHEN_OVERDUE}' THEN 2
            WHEN order.status = '${OrderStatus.NEW}' THEN 3
            WHEN order.status = '${OrderStatus.MODIFIED_ACCEPTED}' THEN 4
            WHEN order.status = '${OrderStatus.PAYING}' THEN 5
            WHEN order.status = '${OrderStatus.IN_DELIVERY_OVERDUE}' THEN 6
            ELSE 999
          END
        `,
          'status_priority',
        )
        .orderBy('status_priority', 'ASC')
        .addOrderBy('order.updatedAt', 'ASC');
    } else {
      queryBuilder.orderBy('order.updatedAt', 'DESC');
    }

    const paginatedOrders: OrderWithCountsResponse = await paginateQueryBuilder(queryBuilder, { page, limit });
    if (includeStatusCounts) {
      paginatedOrders.statusCounts = await this.getOrderStatusCounts(
        user?.id,
        restaurantId || restaurantIdFilter,
        archived,
      );
    }
    if (user) localizeOrderList(paginatedOrders.items, user.language);

    return paginatedOrders;
  }

  private async getOrderStatusCounts(
    userId?: string,
    restaurantId?: string,
    archived?: boolean,
  ): Promise<OrderStatusCounts> {
    const needsActionStatuses = getStatusesByGroups([OrderStatusGroup.NEEDS_ACTION]);
    const inProcessStatuses = getStatusesByGroups([OrderStatusGroup.IN_PROCESS]);
    const completedStatuses = getStatusesByGroups([OrderStatusGroup.COMPLETED]);

    const queryBuilder = this.orderRepository
      .createQueryBuilder('order')
      .select('SUM(CASE WHEN order.status IN (:...needsActionStatuses) THEN 1 ELSE 0 END)', 'needsAction')
      .addSelect('SUM(CASE WHEN order.status IN (:...inProcessStatuses) THEN 1 ELSE 0 END)', 'inProcess')
      .addSelect('SUM(CASE WHEN order.status IN (:...completedStatuses) THEN 1 ELSE 0 END)', 'completed')
      .setParameters({
        needsActionStatuses,
        inProcessStatuses,
        completedStatuses,
      });

    if (userId) {
      queryBuilder.andWhere('order.userId = :userId', { userId });
    }

    if (restaurantId) {
      queryBuilder.andWhere('order.restaurantId = :restaurantId', { restaurantId });
    }

    if (!archived) {
      queryBuilder.andWhere('order.archivedAt IS NULL');
    }

    const result = await queryBuilder.getRawOne();

    return {
      needsAction: parseInt(result?.needsAction || 0),
      inProcess: parseInt(result?.inProcess || 0),
      completed: parseInt(result?.completed || 0),
    };
  }

  async findOne(
    id: string,
    { user, restaurantId, relations }: { user?: UserJwtInfo; restaurantId?: string; relations?: boolean } = {},
  ): Promise<Order> {
    const userId = user?.id;
    const userIdWhere = userId ? { userId } : undefined;
    const restaurantIdWhere = restaurantId ? { restaurantId } : undefined;

    const order = await this.orderRepository.findOne({
      where: { id, ...userIdWhere, ...restaurantIdWhere },
      relations: relations
        ? [
            'orderItems',
            'orderItems.orderItemOptions',
            'orderCustomer',
            'orderItemsOriginal',
            'orderItemsOriginal.orderItemOptionsOriginal',
            'chatConversation',
            'restaurant',
          ]
        : ['orderCustomer'],
    });

    if (!order) {
      throw new NotFoundException('Order not found');
    }

    if (user) localizeOrder(order, user.language);

    return order;
  }

  async cancelOrder(id: string, user?: UserJwtInfo, restaurantId?: string): Promise<boolean> {
    const order = await this.findOne(id, { user, restaurantId });

    // Validate state transition for cancellation
    this.validateStateTransition(order.status, OrderStatus.CANCELLED, 'cancel order');

    const updatedOrder = await this.updateOrderByStatus(order, {
      status: OrderStatus.CANCELLED,
      cancelledAt: new Date(),
      cancelReason: OrderCancelReason.USER_CANCELLED,
      isUserSeen: true,
    });

    // Send notification to staff about order cancellation
    void this.fcmService.sendToStaffWithRestaurantType(updatedOrder, FcmRestaurantType.ORDER_CANCELLED);

    return true;
  }

  async findById(id: string) {
    const order = await this.orderRepository.findOne({ where: { id }, relations: ['user'] });

    if (!order) {
      throw new NotFoundException('Order not found');
    }

    return order;
  }

  async verifyAccessAndGetOrder(user: AllUserJwtInfo, orderId: string): Promise<Order> {
    const order = await this.findById(orderId);

    if (order.archivedAt) {
      throw new BadRequestException('Order is archived');
    }

    switch (user.userType) {
      case UserType.USER:
        if (user.id !== order.userId) {
          throw new BadRequestException('You are not the owner of this order');
        }
        break;
      case UserType.MERCHANT_STAFF:
        if (user.restaurantId !== order.restaurantId) {
          throw new BadRequestException('You are not the staff of this restaurant');
        }
        break;
    }

    return order;
  }

  async acceptOrder(orderId: string, restaurantId: string, setPrepTimeDto: SetPrepTimeDto) {
    const { prepTimeEstimate } = setPrepTimeDto;

    this.logger.log(`Starting acceptOrder for order ${orderId}`, {
      orderId,
      restaurantId,
      prepTimeEstimate,
    });

    // Start a transaction with isolation level SERIALIZABLE
    const paymentStatus = await this.dataSource.transaction('SERIALIZABLE', async (manager) => {
      const order = await this.getOrderAndLock(manager, orderId, restaurantId);

      if (!order) {
        throw new NotFoundException('Order not found');
      }

      this.logger.log(`Order locked and found: ${orderId}`, {
        orderId,
        currentStatus: order.status,
        paymentMethod: order.paymentMethod,
      });

      // Double check status to prevent race condition
      const allowedStatuses = [OrderStatus.NEW, OrderStatus.MODIFIED_ACCEPTED];
      if (!allowedStatuses.includes(order.status)) {
        throw new BadRequestException('Cannot accept order in current state');
      }

      if (order.paymentMethod === PaymentMethod.CREDIT_CARD) {
        const paymentCard = await this.paymentCardService.findByIdAndUserId(order.paymentCardId || '', order.userId);

        // Load user relationship after locking
        await manager
          .createQueryBuilder(Order, 'order')
          .leftJoinAndSelect('order.user', 'user')
          .where('order.id = :orderId', { orderId: order.id })
          .getOne()
          .then((orderWithUser) => {
            if (orderWithUser) {
              order.user = orderWithUser.user;
            }
          });

        // Update status to PAYING before making payment request to prevent multiple payment requests
        const updateResult = await manager.update(
          Order,
          { id: order.id, status: order.status },
          { status: OrderStatus.PAYING },
        );

        if (updateResult.affected === 0) {
          throw new BadRequestException('Order status changed while processing, preventing duplicate payment');
        }

        this.logger.log(`Order status updated to PAYING: ${orderId}`);

        const paymentResult = await this.onepayService.sendPaymentRequest(order, paymentCard);

        this.logger.log(`Payment request completed for order ${orderId}`, {
          orderId,
          paymentResult,
        });

        if (paymentResult === PaymentOnepayStatus.PENDING) return paymentResult;

        const updateData: Partial<Order> = {};
        if (paymentResult === PaymentOnepayStatus.SUCCESS) {
          updateData.paymentStatus = PaymentStatus.PAID;
          updateData.status = OrderStatus.IN_KITCHEN;
          updateData.inKitchenAt = new Date();
          updateData.kitchenEta = new Date(Date.now() + prepTimeEstimate * 60_000);
        } else if (paymentResult === PaymentOnepayStatus.FAILED) {
          updateData.paymentStatus = PaymentStatus.FAILED;
          updateData.status = OrderStatus.CANCELLED;
          updateData.cancelledAt = new Date();
          updateData.cancelReason = OrderCancelReason.PAYMENT_FAILED;
        }

        if (Object.values(updateData).length) {
          await manager.update(Order, { id: order.id }, updateData);
        }

        return paymentResult;
      } else {
        await manager.update(
          Order,
          { id: order.id },
          {
            status: OrderStatus.IN_KITCHEN,
            inKitchenAt: new Date(),
            kitchenEta: new Date(Date.now() + prepTimeEstimate * 60_000),
          },
        );
        return PaymentOnepayStatus.SUCCESS;
      }
    });

    const orderUpdated = await this.findOne(orderId);
    void this.microserviceClient.publishOrderStatusUpdate(orderUpdated);

    if (orderUpdated.status === OrderStatus.IN_KITCHEN) {
      // Send FCM notification to user about order status update
      void this.fcmService.sendToUser(orderUpdated, FcmUserType.ORDER_IN_KITCHEN);
    } else if (orderUpdated.status === OrderStatus.CANCELLED) {
      void this.fcmService.sendToUser(orderUpdated, FcmUserType.ORDER_CANCELLED);
      return orderUpdated;
    }

    this.logger.log(`AcceptOrder completed for order ${orderId}`, {
      orderId,
      paymentStatus,
    });

    return {
      paymentStatus,
      order: orderUpdated,
    };
  }

  async cancelOrderByJob(manager: EntityManager, orderId: string, restaurantId: string) {
    const order = await this.getOrderAndLock(manager, orderId, restaurantId);
    if (!order || order.status !== OrderStatus.PAYING) return;
    if (order.paymentMethod !== PaymentMethod.CREDIT_CARD) return;

    const updateData: Partial<Order> = {};
    updateData.paymentStatus = PaymentStatus.FAILED;
    updateData.status = OrderStatus.CANCELLED;
    updateData.cancelledAt = new Date();
    updateData.cancelReason = OrderCancelReason.PAYMENT_FAILED;

    void this.fcmService.sendToUser(order, FcmUserType.ORDER_CANCELLED);

    await manager.update(Order, { id: order.id }, updateData);
  }

  async notifyOrderStatusUpdate(orderId: string, type: FcmUserType) {
    const order = await this.findOne(orderId);
    void this.fcmService.sendToUser(order, type);
    return order;
  }

  async handlePaymentResultByJob(manager: EntityManager, orderId: string, restaurantId: string, isSuccess: boolean) {
    const order = await this.getOrderAndLock(manager, orderId, restaurantId);

    if (!order) return;
    const allowedStatuses = [OrderStatus.NEW, OrderStatus.MODIFIED_ACCEPTED, OrderStatus.PAYING];
    if (!allowedStatuses.includes(order.status)) return;
    if (order.paymentMethod !== PaymentMethod.CREDIT_CARD) return;

    await manager.update(Order, { id: order.id }, { status: OrderStatus.PAYING });
    const updateData: Partial<Order> = {};

    if (isSuccess) {
      updateData.paymentStatus = PaymentStatus.PAID;
    } else {
      updateData.paymentStatus = PaymentStatus.FAILED;
      updateData.status = OrderStatus.CANCELLED;
      updateData.cancelledAt = new Date();
      updateData.cancelReason = OrderCancelReason.PAYMENT_FAILED;
    }
    await manager.update(Order, { id: order.id }, updateData);
  }

  async findOrderAndUpdateStatus(orderId: string) {
    const orderUpdated = await this.findOne(orderId);
    void this.microserviceClient.publishOrderStatusUpdate(orderUpdated);

    return orderUpdated;
  }

  async rejectOrder(orderId: string, rejectOrderDto: RejectOrderDto, restaurantId: string): Promise<Order> {
    const order = await this.findOne(orderId, { restaurantId });

    // Validate state transition for rejecting order
    this.validateStateTransition(order.status, OrderStatus.CANCELLED, 'reject order');

    const updatedOrder = await this.updateOrderByStatus(order, {
      status: OrderStatus.CANCELLED,
      cancelledAt: new Date(),
      rejectionReason: rejectOrderDto.reason,
      cancelReason: OrderCancelReason.RESTAURANT_REJECTED,
    });

    void this.fcmService.sendToUser(updatedOrder, FcmUserType.ORDER_CANCELLED);
    return updatedOrder;
  }

  async updatePreparingTime(orderId: string, SetPrepTimeDto: SetPrepTimeDto, restaurantId: string): Promise<Order> {
    const order = await this.findOne(orderId, { restaurantId });
    const { prepTimeEstimate } = SetPrepTimeDto;
    // Just check if current state allows prep time updates
    const allowedStatuses = [OrderStatus.IN_KITCHEN, OrderStatus.IN_KITCHEN_OVERDUE];
    if (!allowedStatuses.includes(order.status)) {
      throw new BadRequestException('Cannot update prep time for order in current state');
    }

    const orderUpdated = await this.updateOrderByStatus(order, {
      status: OrderStatus.IN_KITCHEN,
      kitchenEta: new Date(Date.now() + prepTimeEstimate * 60_000),
    });
    void this.fcmService.sendToUser(orderUpdated, FcmUserType.IN_KITCHEN_ETA_UPDATED);
    return orderUpdated;
  }

  async markReadyForDelivery(
    orderId: string,
    setDeliveryEtaDto: SetDeliveryEtaDto,
    restaurantId: string,
  ): Promise<Order> {
    const order = await this.findOne(orderId, { restaurantId });

    const { deliveryTimeEstimateFrom, deliveryTimeEstimateTo } = setDeliveryEtaDto;

    // Validate state transition for marking ready for delivery
    this.validateStateTransition(order.status, OrderStatus.IN_DELIVERY, 'mark ready for delivery');

    const orderUpdated = await this.updateOrderByStatus(order, {
      status: OrderStatus.IN_DELIVERY,
      inDeliveryAt: new Date(),
      deliveryFromEta: new Date(Date.now() + deliveryTimeEstimateFrom * 60_000),
      deliveryToEta: new Date(Date.now() + deliveryTimeEstimateTo * 60_000),
    });

    void this.fcmService.sendToUser(orderUpdated, FcmUserType.ORDER_IN_DELIVERY);
    return orderUpdated;
  }

  async updateDeliveryEta(orderId: string, setDeliveryEtaDto: SetDeliveryEtaDto, restaurantId: string): Promise<Order> {
    const order = await this.findOne(orderId, { restaurantId });

    const { deliveryTimeEstimateFrom, deliveryTimeEstimateTo } = setDeliveryEtaDto;

    // Just check if current state allows ETA updates
    const allowedStatuses = [OrderStatus.IN_DELIVERY, OrderStatus.IN_DELIVERY_OVERDUE];
    if (!allowedStatuses.includes(order.status)) {
      throw new BadRequestException('Cannot update delivery ETA for order in current state');
    }

    const orderUpdated = await this.updateOrderByStatus(order, {
      status: OrderStatus.IN_DELIVERY,
      deliveryFromEta: new Date(Date.now() + deliveryTimeEstimateFrom * 60_000),
      deliveryToEta: new Date(Date.now() + deliveryTimeEstimateTo * 60_000),
    });
    void this.fcmService.sendToUser(orderUpdated, FcmUserType.DELIVERY_ETA_UPDATED);
    return orderUpdated;
  }

  async markAsDelivered(orderId: string, restaurantId: string): Promise<Order> {
    const order = await this.findOne(orderId, { restaurantId });

    // Validate state transition for marking as delivered
    this.validateStateTransition(order.status, OrderStatus.DELIVERED, 'mark as delivered');

    // Use transaction to ensure both order update and restaurant increment succeed together
    await this.dataSource.transaction(async (manager) => {
      // Update order status
      const res = await manager.update(
        Order,
        { id: order.id, status: order.status },
        {
          status: OrderStatus.DELIVERED,
          completedAt: new Date(),
        },
      );

      if (res.affected !== 1) {
        throw new BadRequestException('Failed to update order status: Order changed status while updating');
      }

      // Increment restaurant's total orders sold in same transaction
      await this.restaurantsService.incrementRestaurantTotalOrdersSold(manager, restaurantId);

      // Increment totalOrdersSold for each menu item in the order (batch)
      const orderItems = await manager.find(OrderItem, { where: { orderId: order.id } });
      const menuItemIds = orderItems.map((item) => item.menuItemId);
      await this.menuItemsService.batchIncrementTotalOrdersSold(manager, menuItemIds, 1);
    });

    const orderUpdated = await this.findOne(order.id);

    void this.microserviceClient.publishOrderStatusUpdate(orderUpdated);
    void this.fcmService.sendToUser(orderUpdated, FcmUserType.ORDER_DELIVERED);
    return orderUpdated;
  }

  async markAsUnfulfilled(orderId: string, restaurantId: string): Promise<Order> {
    const order = await this.findOne(orderId, { restaurantId });

    // Validate state transition for marking as unfulfilled
    this.validateStateTransition(order.status, OrderStatus.UNFULFILLED, 'mark as unfulfilled');

    const orderUpdated = await this.updateOrderByStatus(order, {
      status: OrderStatus.UNFULFILLED,
      unfulfilledAt: new Date(),
    });
    void this.fcmService.sendToUser(orderUpdated, FcmUserType.ORDER_UNFULFILLED);
    return orderUpdated;
  }

  async approveModification(
    orderId: string,
    approveModificationDto: ApproveModificationDto,
    user: UserJwtInfo,
  ): Promise<Order> {
    const { approved } = approveModificationDto;
    const order = await this.findOne(orderId, { user });

    // Check modification deadline
    if (order.modificationDeadline && new Date() > order.modificationDeadline) {
      throw new BadRequestException('Modification approval deadline has passed');
    }

    if (approved) {
      // Validate state transition for approving modification
      this.validateStateTransition(order.status, OrderStatus.MODIFIED_ACCEPTED, 'approve modification');

      const updatedOrder = await this.updateOrderByStatus(order, {
        status: OrderStatus.MODIFIED_ACCEPTED,
        acceptedAt: new Date(),
      });

      // Send notification to staff about modification acceptance
      void this.fcmService.sendToStaffWithRestaurantType(updatedOrder, FcmRestaurantType.ORDER_MODIFICATION_ACCEPTED);
      if (user) localizeOrder(updatedOrder, user.language);
      return updatedOrder;
    } else {
      // Validate state transition for rejecting modification (cancel order)
      this.validateStateTransition(order.status, OrderStatus.CANCELLED, 'reject modification');

      const updatedOrder = await this.updateOrderByStatus(order, {
        status: OrderStatus.CANCELLED,
        cancelledAt: new Date(),
        cancelReason: OrderCancelReason.USER_REJECTED_MODIFICATION,
        isUserSeen: true,
      });

      // Send notification to staff about order cancellation
      void this.fcmService.sendToStaffWithRestaurantType(updatedOrder, FcmRestaurantType.ORDER_CANCELLED);
      if (user) localizeOrder(updatedOrder, user.language);
      return updatedOrder;
    }
  }

  async confirmOrderReceived(
    orderId: string,
    confirmReceiptDto: ConfirmOrderReceivedDto,
    user: UserJwtInfo,
  ): Promise<Order> {
    const { received } = confirmReceiptDto;
    const order = await this.findOne(orderId, { user });

    // Check if order is in correct status (in_delivery or in_delivery_overdue)
    const allowedStatuses = [OrderStatus.IN_DELIVERY, OrderStatus.IN_DELIVERY_OVERDUE];
    if (!allowedStatuses.includes(order.status)) {
      throw new BadRequestException('Order must be in delivery or overdue delivery status to confirm receipt');
    }

    // Check if order has passed delivery ETA + 20 minutes
    if (!order.deliveryToEta) {
      throw new BadRequestException('Order must have a delivery ETA to confirm receipt');
    }

    const currentTime = new Date();
    const deliveryEtaPlusWarningTime = new Date(order.deliveryToEta.getTime() - 5 * 60 * 1_000);

    if (currentTime < deliveryEtaPlusWarningTime) {
      throw new BadRequestException(
        `Cannot confirm receipt yet. Please wait ${Math.ceil(
          (deliveryEtaPlusWarningTime.getTime() - currentTime.getTime()) / 60_000,
        )} more minutes before confirming receipt.`,
      );
    }

    const newStatus = received ? OrderStatus.DELIVERED : OrderStatus.NOT_DELIVERED;

    // Use transaction to ensure both order update and restaurant increment succeed together
    await this.dataSource.transaction(async (manager) => {
      // Update order status
      const updateData: Partial<Order> = { status: newStatus };

      if (received) {
        updateData.completedAt = new Date();
      }

      const res = await manager.update(Order, { id: order.id, status: order.status }, updateData);

      if (res.affected !== 1) {
        throw new BadRequestException('Failed to update order status: Order changed status while updating');
      }

      // Increment restaurant's total orders sold only when delivered
      if (received) {
        await this.restaurantsService.incrementRestaurantTotalOrdersSold(manager, order.restaurantId);
        // Increment totalOrdersSold for each menu item in the order (batch)
        const orderItems = await manager.find(OrderItem, { where: { orderId: order.id } });
        const menuItemIds = orderItems.map((item) => item.menuItemId);
        await this.menuItemsService.batchIncrementTotalOrdersSold(manager, menuItemIds, 1);
      }
    });

    const orderUpdated = await this.findOne(order.id, { user });

    void this.microserviceClient.publishOrderStatusUpdate(orderUpdated);
    return orderUpdated;
  }

  async markOrderAsSeen(orderId: string, user: UserJwtInfo): Promise<Order> {
    // Verify user has access to this order
    const order = await this.findOne(orderId, { user });

    if (order.isUserSeen) {
      throw new BadRequestException('Order already marked as seen');
    }

    const allowedStatuses = [OrderStatus.DELIVERED, OrderStatus.CANCELLED, OrderStatus.UNFULFILLED];
    if (!allowedStatuses.includes(order.status)) {
      throw new BadRequestException(`Order must be in ${allowedStatuses.join(', ')} status to mark as seen`);
    }

    // Update order with isUserSeen = true
    await this.orderRepository.update(orderId, {
      isUserSeen: true,
    });

    this.logger.log(`Order ${orderId} has been marked as seen by user ${user.id}`);

    return this.findOne(orderId, { user, relations: true });
  }

  async skipReview(orderId: string, user: UserJwtInfo): Promise<Order> {
    // Verify user has access to this order
    const userId = user.id;
    const order = await this.findOne(orderId, { user });

    if (order.isSkipReview) {
      throw new BadRequestException('Order already skipped review');
    }

    const allowedStatuses = [OrderStatus.DELIVERED];
    if (!allowedStatuses.includes(order.status)) {
      throw new BadRequestException(`Order must be in ${allowedStatuses.join(', ')} status to skip review`);
    }

    // Update order with isUserSeen = true
    await this.orderRepository.update(orderId, {
      isSkipReview: true,
    });

    this.logger.log(`Order ${orderId} has been skipped review by user ${userId}`);

    return this.findOne(orderId, { user, relations: true });
  }

  /**
   * Get latest completed order for user and check if it has been rated
   */
  async getLatestCompletedOrderWithRatingStatus(user: UserJwtInfo): Promise<{
    order: Order | null;
    hasRating: boolean;
    reviewId?: string | null;
  }> {
    const userId = user.id;
    // Get latest completed order for user
    const latestCompletedOrder = await this.orderRepository
      .createQueryBuilder('order')
      .leftJoinAndSelect('order.restaurant', 'restaurant')
      .leftJoinAndSelect('order.orderCustomer', 'orderCustomer')
      .leftJoinAndSelect('order.orderItems', 'orderItems')
      .leftJoinAndSelect('orderItems.orderItemOptions', 'orderItemOptions')
      .where('order.userId = :userId', { userId })
      .andWhere('order.status = :status', { status: OrderStatus.DELIVERED })
      .andWhere('order.completedAt IS NOT NULL')
      .orderBy('order.completedAt', 'DESC')
      .getOne();

    if (!latestCompletedOrder) {
      return {
        order: null,
        hasRating: false,
        reviewId: null,
      };
    }

    if (user) localizeOrder(latestCompletedOrder, user.language);

    // Check if this order has been rated
    const existingReview = await this.restaurantReviewsService.findOneByWhere({
      orderId: latestCompletedOrder.id,
      userId: userId,
    });

    return {
      order: latestCompletedOrder,
      hasRating: !!existingReview,
      reviewId: existingReview?.id || null,
    };
  }

  async modifyOrder(
    orderId: string,
    modifyOrderDto: ModifyOrderDto,
    staffId: string,
    restaurantId: string,
  ): Promise<Order> {
    const order = await this.findOne(orderId, { restaurantId, relations: true });

    if (order.status !== OrderStatus.NEW) {
      throw new BadRequestException('Only orders with status NEW can be modified');
    }

    if (
      modifyOrderDto.editItems?.length &&
      modifyOrderDto.removeItemIds?.length &&
      modifyOrderDto.editItems.some((e) => modifyOrderDto.removeItemIds?.includes(e.orderItemId))
    ) {
      throw new BadRequestException('Cannot edit and remove the same item');
    }

    return this.dataSource.transaction(async (manager) => {
      let subtotal = 0;
      let taxAmount = 0;

      // 1. Handle items to be removed
      if (modifyOrderDto.removeItemIds && modifyOrderDto.removeItemIds.length) {
        for (const itemId of modifyOrderDto.removeItemIds) {
          // Verify the item belongs to this order
          const orderItem = order.orderItems?.find((item) => item.id === itemId);
          if (!orderItem) {
            throw new BadRequestException(
              `Order item with ID ${itemId} does not exist or does not belong to this order`,
            );
          }

          // Delete order item options first
          await manager.softDelete(OrderItemOption, { orderItemId: itemId });

          // Then delete the order item
          await manager.softDelete(OrderItem, { id: itemId });
        }
      }

      // 2. Handle items to be edited
      if (modifyOrderDto.editItems && modifyOrderDto.editItems.length) {
        for (const editItemDto of modifyOrderDto.editItems) {
          // Verify the item belongs to this order
          const orderItem = order.orderItems?.find((item) => item.id === editItemDto.orderItemId);
          if (!orderItem) {
            throw new BadRequestException(
              `Order item with ID ${editItemDto.orderItemId} does not exist or does not belong to this order`,
            );
          }

          const menuItem = await this.menuItemsService.findOneByCartWithRelations(
            restaurantId,
            orderItem.menuSectionId,
            orderItem.menuItemId,
          );

          // Validate menu item options
          this.cartsService.validateMenuItemOptions(menuItem, editItemDto.groupOptions);

          const itemPrice = menuItem?.price ?? menuItem.basePrice;
          let itemTotalPrice = itemPrice * editItemDto.amount;
          const itemTaxAmount = this.calculateTaxAmount(itemTotalPrice, menuItem);
          taxAmount += itemTaxAmount;

          // Update order item base info
          await manager.update(
            OrderItem,
            { id: editItemDto.orderItemId },
            {
              amount: editItemDto.amount,
              price: itemPrice,
              note: editItemDto.note,
              modifiedById: staffId,
              modifiedAt: new Date(),
              taxAmount: itemTaxAmount,
              isAlcohol: menuItem.isAlcohol,
            },
          );

          // Delete existing options and create new ones
          await manager.softDelete(OrderItemOption, { orderItemId: editItemDto.orderItemId });

          if (editItemDto.groupOptions && editItemDto.groupOptions.length) {
            const orderItemOptions: OrderItemOption[] = [];

            for (const groupOption of editItemDto.groupOptions) {
              const menuItemOptionGroup = menuItem.menuItemOptionGroups?.find(
                (g) => g.id === groupOption.optionGroupId,
              );

              if (!menuItemOptionGroup) {
                throw new BadRequestException(`Option group ${groupOption.optionGroupId} not found`);
              }

              for (const option of groupOption.options) {
                const menuItemOption = menuItemOptionGroup.menuItemOptions?.find((o) => o.id === option.id);

                if (!menuItemOption) {
                  throw new BadRequestException(`Option ${option.id} not found`);
                }

                const optionPrice = menuItemOption.price ?? menuItemOption.basePrice;
                const optionAmount = option.amount || 1;
                const optionTotalPrice = optionPrice * optionAmount;

                const optionTaxAmount = this.calculateTaxAmount(optionTotalPrice, menuItemOption);
                taxAmount += optionTaxAmount;

                itemTotalPrice += optionTotalPrice;

                const orderItemOption = manager.create(OrderItemOption, {
                  orderItemId: editItemDto.orderItemId,
                  menuItemOptionGroupId: groupOption.optionGroupId,
                  menuItemOptionId: option.id,
                  amount: optionAmount,
                  price: optionPrice,
                  optionGroupName: menuItemOptionGroup.publishedName,
                  optionGroupNameVi: menuItemOptionGroup.publishedNameVi,
                  optionGroupNameEn: menuItemOptionGroup.publishedNameEn,
                  optionName: menuItemOption.publishedName,
                  optionNameVi: menuItemOption.publishedNameVi,
                  optionNameEn: menuItemOption.publishedNameEn,
                  taxAmount: optionTaxAmount,
                  isAlcohol: menuItemOption.isAlcohol,
                } as OrderItemOption);

                orderItemOptions.push(orderItemOption);
              }
            }

            if (orderItemOptions.length > 0) {
              await manager.save(orderItemOptions);
            }
          }

          subtotal += itemTotalPrice;
        }
      }

      // 3. Handle items to be created
      if (modifyOrderDto.createItems && modifyOrderDto.createItems.length) {
        for (const createItemDto of modifyOrderDto.createItems) {
          const menuItem = await this.menuItemsService.findOneByCartWithRelations(
            restaurantId,
            createItemDto.menuSectionId,
            createItemDto.menuItemId,
          );

          // Validate menu item belongs to restaurant
          this.cartsService.validateMenuItemBelongsToRestaurant(menuItem, restaurantId);

          // Validate menu item options
          this.cartsService.validateMenuItemOptions(menuItem, createItemDto.groupOptions);

          const menuSection = menuItem.menuSections?.find((s) => s.id === createItemDto.menuSectionId);

          if (!menuSection) {
            throw new BadRequestException(`Menu section ${createItemDto.menuSectionId} not found`);
          }

          const itemPrice = menuItem?.price ?? menuItem.basePrice;
          let itemTotalPrice = itemPrice * createItemDto.amount;
          const itemTaxAmount = this.calculateTaxAmount(itemTotalPrice, menuItem);
          taxAmount += itemTaxAmount;

          const orderItem = manager.create(OrderItem, {
            orderId: order.id,
            menuItemId: menuItem.id,
            menuSectionId: menuSection.id,
            amount: createItemDto.amount,
            price: itemPrice,
            taxAmount: itemTaxAmount,
            note: createItemDto.note,
            menuItemName: menuItem.publishedName,
            menuItemNameVi: menuItem.publishedNameVi,
            menuItemNameEn: menuItem.publishedNameEn,
            menuSectionName: menuSection.publishedName,
            menuSectionNameVi: menuSection.publishedNameVi,
            menuSectionNameEn: menuSection.publishedNameEn,
            menuItemDescription: menuItem.description,
            menuItemDescriptionVi: menuItem.descriptionVi,
            menuItemDescriptionEn: menuItem.descriptionEn,
            menuItemImageUrls: menuItem.imageUrls,
            isAlcohol: menuItem.isAlcohol,
            modifiedById: staffId,
            modifiedAt: new Date(),
          } as OrderItem);

          const savedOrderItem = await manager.save(orderItem);

          if (createItemDto.groupOptions && createItemDto.groupOptions.length) {
            const orderItemOptions: OrderItemOption[] = [];

            for (const groupOption of createItemDto.groupOptions) {
              const menuItemOptionGroup = menuItem.menuItemOptionGroups?.find(
                (g) => g.id === groupOption.optionGroupId,
              );

              if (!menuItemOptionGroup) {
                throw new BadRequestException(`Option group ${groupOption.optionGroupId} not found`);
              }

              for (const option of groupOption.options) {
                const menuItemOption = menuItemOptionGroup.menuItemOptions?.find((o) => o.id === option.id);

                if (!menuItemOption) {
                  throw new BadRequestException(`Option ${option.id} not found`);
                }

                const optionPrice = menuItemOption.price ?? menuItemOption.basePrice;
                const optionAmount = option.amount || 1;
                const optionTotalPrice = optionPrice * optionAmount;

                const optionTaxAmount = this.calculateTaxAmount(optionTotalPrice, menuItemOption);
                taxAmount += optionTaxAmount;

                itemTotalPrice += optionTotalPrice;

                const orderItemOption = manager.create(OrderItemOption, {
                  orderItemId: savedOrderItem.id,
                  menuItemOptionGroupId: groupOption.optionGroupId,
                  menuItemOptionId: option.id,
                  amount: optionAmount,
                  price: optionPrice,
                  taxAmount: optionTaxAmount,
                  optionGroupName: menuItemOptionGroup.publishedName,
                  optionGroupNameVi: menuItemOptionGroup.publishedNameVi,
                  optionGroupNameEn: menuItemOptionGroup.publishedNameEn,
                  optionName: menuItemOption.publishedName,
                  optionNameVi: menuItemOption.publishedNameVi,
                  optionNameEn: menuItemOption.publishedNameEn,
                  isAlcohol: menuItemOption.isAlcohol,
                } as OrderItemOption);

                orderItemOptions.push(orderItemOption);
              }
            }

            if (orderItemOptions.length > 0) {
              await manager.save(orderItemOptions);
            }
          }

          subtotal += itemTotalPrice;
        }
      }

      // Calculate subtotal for remaining items (not removed or edited)
      const untouchedItems =
        order.orderItems?.filter((item) => {
          // Items that are not in removeItemIds and not in editItems
          const isRemoved = modifyOrderDto.removeItemIds?.includes(item.id) || false;
          const isEdited = modifyOrderDto.editItems?.some((editItem) => editItem.orderItemId === item.id) || false;
          return !isRemoved && !isEdited;
        }) || [];

      for (const item of untouchedItems) {
        let itemTotalPrice = item.price * item.amount;

        // Add option prices
        if (item.orderItemOptions && item.orderItemOptions.length) {
          for (const option of item.orderItemOptions) {
            itemTotalPrice += option.price * option.amount;
          }
        }

        subtotal += itemTotalPrice;
      }

      const total = Number((subtotal + taxAmount + order.shippingFee).toFixed(0));

      await manager.update(
        Order,
        { id: order.id },
        {
          subtotal,
          taxAmount,
          total,
          status: OrderStatus.MODIFIED,
          modifiedAt: new Date(),
        },
      );

      // Get updated order with all relations
      const updatedOrder = await manager.findOne(Order, {
        where: { id: order.id },
        relations: [
          'orderItems',
          'orderItems.orderItemOptions',
          'orderItems.modifiedBy',
          'orderCustomer',
          'orderItemsOriginal',
          'orderItemsOriginal.orderItemOptionsOriginal',
        ],
      });

      if (!updatedOrder) {
        throw new NotFoundException('Order not found');
      }

      void this.fcmService.sendToUser(updatedOrder, FcmUserType.ORDER_MODIFIED);
      return updatedOrder;
    });
  }

  async estimateShippingFee(query: EstimateShippingFeeDto, userId: string) {
    const { restaurantId, addressId } = query;
    const address = await this.userAddressesService.findOneByUserAndId(addressId, userId);
    if (!address) return {};
    const geofencings = await this.geofencingService.findGeofencingForPoint(
      restaurantId,
      address.latitude,
      address.longitude,
    );
    if (!geofencings || geofencings.length === 0) return {};
    // Lấy geofencing có shippingFee nhỏ nhất
    const cheapest = geofencings.reduce(
      (min, g) => (Number(g.shippingFee) < Number(min.shippingFee) ? g : min),
      geofencings[0],
    );
    return {
      geofencingId: cheapest.id,
      name: cheapest.name,
      shippingFee: cheapest.shippingFee,
      type: cheapest.type,
      description: cheapest.description,
    };
  }

  private async updateOrderByStatus(order: Order, data: QueryDeepPartialEntity<Order>) {
    const res = await this.orderRepository.update({ id: order.id, status: order.status }, data);
    if (res.affected === 1) {
      const orderUpdated = await this.findOne(order.id);
      void this.microserviceClient.publishOrderStatusUpdate(orderUpdated);
      return orderUpdated;
    }

    throw new BadRequestException('Failed to update order status: Order changed status while updating');
  }

  private async getOrderAndLock(manager: EntityManager, orderId: string, restaurantId: string) {
    // Lock the order row with SELECT FOR UPDATE
    return await manager
      .createQueryBuilder(Order, 'order')
      .setLock('pessimistic_write')
      .where('order.id = :orderId AND order.restaurantId = :restaurantId', {
        orderId,
        ...(restaurantId ? { restaurantId } : {}),
      })
      .getOne();
  }

  /**
   * Validate if restaurant is currently open for ordering
   * @param restaurant The restaurant entity with availableSchedule
   * @throws BadRequestException if restaurant is closed
   */
  private validateRestaurantIsOpen(restaurant: Restaurant): void {
    const { currentTime, currentDayOfWeek } = getCurrentTimeByTimeAndDay();

    if (!restaurant.availableSchedule || restaurant.availableSchedule.length === 0) {
      throw new BadRequestException('Restaurant schedule is not configured');
    }

    const isOpen = restaurant.availableSchedule.some((schedule: any) => {
      if (schedule.day !== currentDayOfWeek) {
        return false;
      }

      if (schedule.isAllDay) {
        return true;
      }

      if (!schedule.start || !schedule.end) {
        return false;
      }

      return currentTime >= schedule.start && currentTime <= schedule.end;
    });

    if (!isOpen) {
      throw new BadRequestException('Restaurant is currently closed');
    }
  }

  /**
   * Validate if menu section is currently open for ordering
   * @param menuSection The menu section entity with availableSchedule
   * @throws BadRequestException if menu section is closed
   */
  private validateMenuSectionIsOpen(menuSection: MenuSection | undefined): void {
    if (!menuSection) {
      throw new BadRequestException('Menu section not found');
    }

    const { currentTime, currentDayOfWeek } = getCurrentTimeByTimeAndDay();

    if (!menuSection.availableSchedule || menuSection.availableSchedule.length === 0) {
      throw new BadRequestException(`Menu section "${menuSection.publishedName}" schedule is not configured`);
    }

    const isOpen = menuSection.availableSchedule.some((schedule: any) => {
      if (schedule.day !== currentDayOfWeek) {
        return false;
      }

      if (schedule.isAllDay) {
        return true;
      }

      if (!schedule.start || !schedule.end) {
        return false;
      }

      return currentTime >= schedule.start && currentTime <= schedule.end;
    });

    if (!isOpen) {
      throw new BadRequestException(`Menu section "${menuSection.publishedName}" is currently closed`);
    }
  }
}
