import { IsEnum, IsOptional, IsString, IsUUID } from 'class-validator';

import { ApiProperty } from '@nestjs/swagger';

import { TranslateType } from './get-translates.dto';

export class TranslateItemDto {
  @ApiProperty({ description: 'ID of the entity' })
  @IsUUID()
  id: string;

  @ApiProperty({ description: 'Type of entity', enum: TranslateType })
  @IsEnum(TranslateType)
  type: TranslateType;

  @ApiProperty({ description: 'Published name', required: false })
  @IsOptional()
  @IsString()
  publishedName?: string;

  @ApiProperty({ description: 'Published name in English', required: false })
  @IsOptional()
  @IsString()
  publishedNameEn?: string;

  @ApiProperty({ description: 'Published name in Vietnamese', required: false })
  @IsOptional()
  @IsString()
  publishedNameVi?: string;

  @ApiProperty({ description: 'Description', required: false })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ description: 'Description in English', required: false })
  @IsOptional()
  @IsString()
  descriptionEn?: string;

  @ApiProperty({ description: 'Description in Vietnamese', required: false })
  @IsOptional()
  @IsString()
  descriptionVi?: string;
}
