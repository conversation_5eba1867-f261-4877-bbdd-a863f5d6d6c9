import { Type } from 'class-transformer';
import { IsArray, IsUUID, ValidateNested } from 'class-validator';

import { ApiProperty } from '@nestjs/swagger';

import { TranslateItemDto } from './translate-item.dto';

export class UpdateTranslatesDto {
  @ApiProperty({ description: 'Restaurant ID' })
  @IsUUID()
  restaurantId: string;

  @ApiProperty({
    description: 'Array of translation items to update',
    type: [TranslateItemDto],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => TranslateItemDto)
  items: TranslateItemDto[];
}
