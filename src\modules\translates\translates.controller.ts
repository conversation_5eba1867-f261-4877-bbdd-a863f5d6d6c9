import { UserMerchantId } from '@/common/decorators/user.decorator';
import { Language } from '@/common/enums/language.enum';
import { Roles } from '@auth/decorators/roles.decorator';
import { UserType } from '@auth/enums/user-type.enum';
import { Body, Controller, Get, Put, Query } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';

import { Public } from '../auth/decorators/public.decorator';
import { GetTranslatesDto } from './dtos/get-translates.dto';
import { UpdateTranslatesDto } from './dtos/update-translates.dto';
import { TranslateResponseItem, TranslatesService } from './translates.service';

@ApiTags('Translates')
@Controller('translates')
@Roles({ userType: UserType.MERCHANT_USER, role: '*' })
export class TranslatesController {
  constructor(private readonly translatesService: TranslatesService) {}

  @Get()
  async getTranslates(
    @Query() dto: GetTranslatesDto,
    @UserMerchantId() ownerId: string | null,
  ): Promise<TranslateResponseItem[]> {
    return this.translatesService.getTranslates(dto, ownerId);
  }

  @Put()
  async updateTranslates(
    @Body() dto: UpdateTranslatesDto,
    @UserMerchantId() ownerId: string | null,
  ): Promise<{ updated: number }> {
    return this.translatesService.updateTranslates(dto, ownerId);
  }

  @Public()
  @Get('list-languages')
  getListLanguages() {
    return {
      languages: {
        [Language.EN]: 'English',
        [Language.VI]: 'Tiếng Việt',
      },

      fields: {
        publishedName: {
          [Language.EN]: 'publishedNameEn',
          [Language.VI]: 'publishedNameVi',
        },
        description: {
          [Language.EN]: 'descriptionEn',
          [Language.VI]: 'descriptionVi',
        },
      },
    };
  }
}
