import { In, Repository } from 'typeorm';

import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';

import { MenuItemOptionGroup } from '../menu-item-option-groups/entities/menu-item-option-group.entity';
import { MenuItem } from '../menu-items/entities/menu-item.entity';
import { MenuSection } from '../menu-sections/entities/menu-section.entity';
import { RestaurantAccessService } from '../shared/restaurant-access/restaurant-access.service';
import { GetTranslatesDto, TranslateType } from './dtos/get-translates.dto';
import { UpdateTranslatesDto } from './dtos/update-translates.dto';

export interface TranslateResponseItem {
  id: string;
  type: TranslateType;
  publishedName: string;
  publishedNameEn?: string | null;
  publishedNameVi?: string | null;
  description?: string | null;
  descriptionEn?: string | null;
  descriptionVi?: string | null;
}

@Injectable()
export class TranslatesService {
  constructor(
    @InjectRepository(MenuSection)
    private readonly menuSectionRepository: Repository<MenuSection>,
    @InjectRepository(MenuItem)
    private readonly menuItemRepository: Repository<MenuItem>,
    @InjectRepository(MenuItemOptionGroup)
    private readonly menuItemOptionGroupRepository: Repository<MenuItemOptionGroup>,
    private readonly restaurantAccessService: RestaurantAccessService,
  ) {}

  async getTranslates(dto: GetTranslatesDto, ownerId: string | null): Promise<TranslateResponseItem[]> {
    // Verify restaurant access
    await this.restaurantAccessService.verifyAccessRestaurant(dto.restaurantId, ownerId);

    const results: TranslateResponseItem[] = [];

    // Get menu sections
    if (!dto.type || dto.type === TranslateType.MENU_SECTION) {
      const menuSections = await this.menuSectionRepository.find({
        where: { restaurantId: dto.restaurantId },
        select: ['id', 'publishedName', 'publishedNameEn', 'publishedNameVi'],
      });

      results.push(
        ...menuSections.map((section) => ({
          id: section.id,
          type: TranslateType.MENU_SECTION,
          publishedName: section.publishedName,
          publishedNameEn: section.publishedNameEn,
          publishedNameVi: section.publishedNameVi,
        })),
      );
    }

    // Get menu items
    if (!dto.type || dto.type === TranslateType.MENU_ITEM) {
      const menuItems = await this.menuItemRepository.find({
        where: { restaurantId: dto.restaurantId },
        select: [
          'id',
          'publishedName',
          'publishedNameEn',
          'publishedNameVi',
          'description',
          'descriptionEn',
          'descriptionVi',
        ],
      });

      results.push(
        ...menuItems.map((item) => ({
          id: item.id,
          type: TranslateType.MENU_ITEM,
          publishedName: item.publishedName,
          publishedNameEn: item.publishedNameEn,
          publishedNameVi: item.publishedNameVi,
          description: item.description,
          descriptionEn: item.descriptionEn,
          descriptionVi: item.descriptionVi,
        })),
      );
    }

    // Get menu item option groups
    if (!dto.type || dto.type === TranslateType.MENU_ITEM_OPTION_GROUP) {
      const optionGroups = await this.menuItemOptionGroupRepository.find({
        where: { restaurantId: dto.restaurantId },
        select: ['id', 'publishedName', 'publishedNameEn', 'publishedNameVi'],
      });

      results.push(
        ...optionGroups.map((group) => ({
          id: group.id,
          type: TranslateType.MENU_ITEM_OPTION_GROUP,
          publishedName: group.publishedName,
          publishedNameEn: group.publishedNameEn,
          publishedNameVi: group.publishedNameVi,
        })),
      );
    }

    return results;
  }

  async updateTranslates(dto: UpdateTranslatesDto, ownerId: string | null): Promise<{ updated: number }> {
    const { restaurantId, items } = dto;
    // Verify restaurant access
    await this.restaurantAccessService.verifyAccessRestaurant(restaurantId, ownerId);

    // Group items by type for batch processing
    const menuSectionUpdates: Array<{ id: string; data: any }> = [];
    const menuItemUpdates: Array<{ id: string; data: any }> = [];
    const menuItemOptionGroupUpdates: Array<{ id: string; data: any }> = [];

    // Helper function to remove undefined values
    const cleanUpdateData = (data: any): any => {
      const cleaned = {};
      Object.keys(data).forEach((key) => {
        if (data[key] !== undefined) {
          cleaned[key] = data[key];
        }
      });
      return cleaned;
    };

    // Process and group items by type
    for (const item of items) {
      let updateData: any = {};

      switch (item.type) {
        case TranslateType.MENU_SECTION:
          updateData = cleanUpdateData({
            publishedName: item.publishedName,
            publishedNameEn: item.publishedNameEn,
            publishedNameVi: item.publishedNameVi,
          });
          if (Object.keys(updateData).length > 0) {
            menuSectionUpdates.push({ id: item.id, data: updateData });
          }
          break;

        case TranslateType.MENU_ITEM:
          updateData = cleanUpdateData({
            publishedName: item.publishedName,
            publishedNameEn: item.publishedNameEn,
            publishedNameVi: item.publishedNameVi,
            description: item.description,
            descriptionEn: item.descriptionEn,
            descriptionVi: item.descriptionVi,
          });
          if (Object.keys(updateData).length > 0) {
            menuItemUpdates.push({ id: item.id, data: updateData });
          }
          break;

        case TranslateType.MENU_ITEM_OPTION_GROUP:
          updateData = cleanUpdateData({
            publishedName: item.publishedName,
            publishedNameEn: item.publishedNameEn,
            publishedNameVi: item.publishedNameVi,
          });
          if (Object.keys(updateData).length > 0) {
            menuItemOptionGroupUpdates.push({ id: item.id, data: updateData });
          }
          break;
      }
    }

    let updatedCount = 0;

    // Batch update menu sections
    if (menuSectionUpdates.length > 0) {
      await this.batchUpdateEntities(
        this.menuSectionRepository,
        menuSectionUpdates,
        restaurantId,
        TranslateType.MENU_SECTION,
      );
      updatedCount += menuSectionUpdates.length;
    }

    // Batch update menu items
    if (menuItemUpdates.length > 0) {
      await this.batchUpdateEntities(this.menuItemRepository, menuItemUpdates, restaurantId, TranslateType.MENU_ITEM);
      updatedCount += menuItemUpdates.length;
    }

    // Batch update menu item option groups
    if (menuItemOptionGroupUpdates.length > 0) {
      await this.batchUpdateEntities(
        this.menuItemOptionGroupRepository,
        menuItemOptionGroupUpdates,
        restaurantId,
        TranslateType.MENU_ITEM_OPTION_GROUP,
      );
      updatedCount += menuItemOptionGroupUpdates.length;
    }

    return { updated: updatedCount };
  }

  private async batchUpdateEntities(
    repository: Repository<any>,
    updates: Array<{ id: string; data: any }>,
    restaurantId: string,
    entityType: TranslateType,
  ): Promise<void> {
    // Get all entities to verify they exist and check restaurant access
    const entityIds = updates.map((update) => update.id);
    const entities = await repository.find({
      where: { id: In(entityIds), restaurantId },
      select: ['id', 'restaurantId'],
    });

    if (entities.length !== entityIds.length) {
      const foundIds = entities.map((entity) => entity.id);
      const missingIds = entityIds.filter((id) => !foundIds.includes(id));
      throw new NotFoundException(`Entities with ids ${missingIds.join(', ')} and type ${entityType} not found`);
    }

    // Perform batch updates
    const updatePromises = updates.map((update) => repository.update(update.id, update.data));
    await Promise.all(updatePromises);
  }
}
